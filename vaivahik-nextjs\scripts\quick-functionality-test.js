#!/usr/bin/env node

/**
 * Quick Functionality Test Script
 * Tests basic functionality without complex dependencies
 */

const fs = require('fs');
const path = require('path');
const http = require('http');

// Color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m'
};

const log = (message, color = 'white') => {
  console.log(`${colors[color]}${message}${colors.reset}`);
};

class QuickTester {
  constructor() {
    this.results = {
      files: {},
      pages: {},
      apis: {},
      summary: {
        total: 0,
        passed: 0,
        failed: 0
      }
    };
  }

  // Test if critical files exist
  testFileStructure() {
    log('\n📁 Testing File Structure...', 'bright');
    
    const criticalFiles = [
      // Admin Pages
      'src/pages/admin/dashboard.js',
      'src/pages/admin/users.js',
      'src/pages/admin/content-management.js',
      'src/pages/admin/verification-queue.js',
      'src/pages/admin/premium-plans.js',
      
      // Website Pages
      'src/pages/website/dashboard.js',
      'src/website/landing-page-new.js',
      'src/website/pages/register.js',
      'src/website/pages/login.js',
      
      // API Routes
      'src/pages/api/auth/login.js',
      'src/pages/api/users/register.js',
      'src/pages/api/content-pages.js',
      'src/pages/api/social-media-links.js',
      
      // Components
      'src/components/admin/EnhancedAdminLayout.js',
      'src/components/enhanced/EnhancedMatchDashboard.js',
      
      // Config Files
      'src/config/apiConfig.js',
      'src/utils/axiosConfig.js',
      'next.config.js',
      'package.json'
    ];

    criticalFiles.forEach(file => {
      const fullPath = path.join(process.cwd(), file);
      const exists = fs.existsSync(fullPath);
      
      this.results.files[file] = {
        exists,
        path: fullPath,
        size: exists ? fs.statSync(fullPath).size : 0
      };
      
      this.updateSummary(exists);
      
      if (exists) {
        log(`  ✅ ${file}`, 'green');
      } else {
        log(`  ❌ ${file}`, 'red');
      }
    });
  }

  // Test if server is running
  async testServerStatus() {
    log('\n🌐 Testing Server Status...', 'bright');
    
    const servers = [
      { name: 'Next.js Frontend', port: 3000, url: 'http://localhost:3000' },
      { name: 'Express Backend', port: 8000, url: 'http://localhost:8000' }
    ];

    for (const server of servers) {
      try {
        const isRunning = await this.checkPort(server.port);
        this.results.pages[server.name] = { running: isRunning, port: server.port };
        this.updateSummary(isRunning);
        
        if (isRunning) {
          log(`  ✅ ${server.name} (port ${server.port})`, 'green');
        } else {
          log(`  ❌ ${server.name} (port ${server.port}) - Not running`, 'red');
        }
      } catch (error) {
        log(`  ❌ ${server.name} - Error: ${error.message}`, 'red');
        this.updateSummary(false);
      }
    }
  }

  // Check if a port is in use
  checkPort(port) {
    return new Promise((resolve) => {
      const server = http.createServer();
      
      server.listen(port, () => {
        server.close(() => {
          resolve(false); // Port is free, so service is not running
        });
      });
      
      server.on('error', () => {
        resolve(true); // Port is in use, so service is likely running
      });
    });
  }

  // Test basic API endpoints
  async testBasicAPIs() {
    log('\n🔌 Testing Basic API Endpoints...', 'bright');
    
    const endpoints = [
      { name: 'Content Pages API', url: 'http://localhost:3000/api/content-pages' },
      { name: 'Social Media API', url: 'http://localhost:3000/api/social-media-links' },
      { name: 'Health Check', url: 'http://localhost:3000/api/hello' }
    ];

    for (const endpoint of endpoints) {
      try {
        const response = await this.makeRequest(endpoint.url);
        const success = response.status >= 200 && response.status < 400;
        
        this.results.apis[endpoint.name] = {
          success,
          status: response.status,
          url: endpoint.url
        };
        
        this.updateSummary(success);
        
        if (success) {
          log(`  ✅ ${endpoint.name} - ${response.status}`, 'green');
        } else {
          log(`  ❌ ${endpoint.name} - ${response.status}`, 'red');
        }
      } catch (error) {
        log(`  ❌ ${endpoint.name} - ${error.message}`, 'red');
        this.updateSummary(false);
      }
    }
  }

  // Simple HTTP request
  makeRequest(url) {
    return new Promise((resolve, reject) => {
      const urlObj = new URL(url);
      const options = {
        hostname: urlObj.hostname,
        port: urlObj.port,
        path: urlObj.pathname,
        method: 'GET',
        timeout: 5000
      };

      const req = http.request(options, (res) => {
        resolve({ status: res.statusCode, headers: res.headers });
      });

      req.on('error', reject);
      req.on('timeout', () => reject(new Error('Request timeout')));
      req.end();
    });
  }

  // Test package.json dependencies
  testDependencies() {
    log('\n📦 Testing Dependencies...', 'bright');
    
    try {
      const packagePath = path.join(process.cwd(), 'package.json');
      const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
      
      const criticalDeps = [
        'next',
        'react',
        '@mui/material',
        '@mui/icons-material',
        'axios'
      ];

      criticalDeps.forEach(dep => {
        const hasInDeps = packageJson.dependencies && packageJson.dependencies[dep];
        const hasInDevDeps = packageJson.devDependencies && packageJson.devDependencies[dep];
        const exists = hasInDeps || hasInDevDeps;
        
        this.updateSummary(exists);
        
        if (exists) {
          const version = hasInDeps || hasInDevDeps;
          log(`  ✅ ${dep} (${version})`, 'green');
        } else {
          log(`  ❌ ${dep} - Missing`, 'red');
        }
      });
    } catch (error) {
      log(`  ❌ Error reading package.json: ${error.message}`, 'red');
    }
  }

  // Test configuration files
  testConfiguration() {
    log('\n⚙️  Testing Configuration...', 'bright');
    
    const configTests = [
      {
        name: 'API Config',
        file: 'src/config/apiConfig.js',
        test: (content) => content.includes('API_BASE_URL')
      },
      {
        name: 'Next.js Config',
        file: 'next.config.js',
        test: (content) => content.includes('nextConfig')
      },
      {
        name: 'Axios Config',
        file: 'src/utils/axiosConfig.js',
        test: (content) => content.includes('axios')
      }
    ];

    configTests.forEach(test => {
      try {
        const filePath = path.join(process.cwd(), test.file);
        if (fs.existsSync(filePath)) {
          const content = fs.readFileSync(filePath, 'utf8');
          const valid = test.test(content);
          
          this.updateSummary(valid);
          
          if (valid) {
            log(`  ✅ ${test.name}`, 'green');
          } else {
            log(`  ⚠️  ${test.name} - Configuration may be incomplete`, 'yellow');
          }
        } else {
          log(`  ❌ ${test.name} - File missing`, 'red');
          this.updateSummary(false);
        }
      } catch (error) {
        log(`  ❌ ${test.name} - Error: ${error.message}`, 'red');
        this.updateSummary(false);
      }
    });
  }

  updateSummary(success) {
    this.results.summary.total++;
    if (success) {
      this.results.summary.passed++;
    } else {
      this.results.summary.failed++;
    }
  }

  async runAllTests() {
    log('🎯 Starting Quick Functionality Test', 'bright');
    log('=' .repeat(50), 'cyan');
    
    this.testFileStructure();
    await this.testServerStatus();
    await this.testBasicAPIs();
    this.testDependencies();
    this.testConfiguration();
    
    this.displaySummary();
    this.saveReport();
  }

  displaySummary() {
    const { summary } = this.results;
    const successRate = ((summary.passed / summary.total) * 100).toFixed(1);
    
    log('\n📊 QUICK TEST SUMMARY', 'bright');
    log('=' .repeat(30), 'cyan');
    log(`Total Tests: ${summary.total}`, 'white');
    log(`✅ Passed: ${summary.passed}`, 'green');
    log(`❌ Failed: ${summary.failed}`, 'red');
    log(`📈 Success Rate: ${successRate}%`, successRate > 80 ? 'green' : 'yellow');
    
    // Recommendations
    if (successRate > 90) {
      log('\n🎉 EXCELLENT! Core functionality is working well.', 'green');
    } else if (successRate > 70) {
      log('\n👍 GOOD! Minor issues detected.', 'yellow');
    } else {
      log('\n⚠️  NEEDS ATTENTION! Multiple issues found.', 'red');
    }
    
    // Next steps
    log('\n🚀 Next Steps:', 'bright');
    if (summary.failed > 0) {
      log('1. Fix missing files and configurations', 'white');
      log('2. Start required servers (npm run dev)', 'white');
      log('3. Run full test suite once basics are working', 'white');
    } else {
      log('1. Run comprehensive test suite: npm run test:all', 'white');
      log('2. Check detailed functionality with browser tests', 'white');
    }
  }

  saveReport() {
    const reportPath = path.join(process.cwd(), 'test-reports', 'quick-test-report.json');
    
    // Create directory if it doesn't exist
    const reportDir = path.dirname(reportPath);
    if (!fs.existsSync(reportDir)) {
      fs.mkdirSync(reportDir, { recursive: true });
    }
    
    const report = {
      timestamp: new Date().toISOString(),
      testType: 'Quick Functionality Test',
      results: this.results,
      recommendations: this.generateRecommendations()
    };
    
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    log(`\n📄 Report saved to: ${reportPath}`, 'cyan');
  }

  generateRecommendations() {
    const recommendations = [];
    
    // Check for missing critical files
    const missingFiles = Object.entries(this.results.files)
      .filter(([_, file]) => !file.exists)
      .map(([name, _]) => name);
    
    if (missingFiles.length > 0) {
      recommendations.push(`Missing ${missingFiles.length} critical files`);
    }
    
    // Check server status
    const downServers = Object.entries(this.results.pages)
      .filter(([_, server]) => !server.running)
      .map(([name, _]) => name);
    
    if (downServers.length > 0) {
      recommendations.push(`${downServers.length} servers are not running`);
    }
    
    // Check API status
    const failedAPIs = Object.entries(this.results.apis)
      .filter(([_, api]) => !api.success)
      .map(([name, _]) => name);
    
    if (failedAPIs.length > 0) {
      recommendations.push(`${failedAPIs.length} API endpoints are failing`);
    }
    
    return recommendations;
  }
}

// Main execution
async function main() {
  const tester = new QuickTester();
  
  try {
    await tester.runAllTests();
  } catch (error) {
    log(`💥 Quick test failed: ${error.message}`, 'red');
    console.error(error);
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { QuickTester };
