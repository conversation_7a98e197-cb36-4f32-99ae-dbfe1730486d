#!/usr/bin/env node

/**
 * Detailed Functionality Audit Script
 * Analyzes actual code to determine what's implemented vs what's missing
 */

const fs = require('fs');
const path = require('path');

// Color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m'
};

const log = (message, color = 'white') => {
  console.log(`${colors[color]}${message}${colors.reset}`);
};

class DetailedAuditor {
  constructor() {
    this.results = {
      admin: {
        pages: {},
        components: {},
        apis: {},
        functionality: {}
      },
      website: {
        pages: {},
        components: {},
        apis: {},
        functionality: {}
      },
      backend: {
        apis: {},
        routes: {},
        functionality: {}
      },
      summary: {
        totalFeatures: 0,
        implementedFeatures: 0,
        partialFeatures: 0,
        missingFeatures: 0
      }
    };
  }

  // Analyze admin functionality
  analyzeAdminFunctionality() {
    log('\n👨‍💼 Analyzing Admin Panel Functionality...', 'bright');
    
    const adminFeatures = {
      'User Management': {
        files: ['src/pages/admin/users.js', 'src/pages/admin/verification-queue.js'],
        apis: ['src/pages/api/admin/users.js', 'src/pages/api/admin/verification-queue.js'],
        components: ['src/components/admin/UserManagement.js']
      },
      'Content Management': {
        files: ['src/pages/admin/content-management.js'],
        apis: ['src/pages/api/content-pages.js', 'src/pages/api/social-media-links.js'],
        components: ['src/components/admin/ContentEditor.js']
      },
      'Premium Plans': {
        files: ['src/pages/admin/premium-plans.js'],
        apis: ['src/pages/api/admin/premium-plans.js'],
        components: ['src/components/admin/PlanEditor.js']
      },
      'Photo Moderation': {
        files: ['src/pages/admin/photo-moderation.js'],
        apis: ['src/pages/api/admin/photo-moderation.js'],
        components: ['src/components/admin/PhotoModerator.js']
      },
      'Analytics & Reports': {
        files: ['src/pages/admin/advanced-analytics/index.js', 'src/pages/admin/revenue-reports.js'],
        apis: ['src/pages/api/admin/analytics.js'],
        components: ['src/components/admin/Analytics.js']
      },
      'System Settings': {
        files: ['src/pages/admin/settings.js', 'src/pages/admin/feature-flags.js'],
        apis: ['src/pages/api/admin/settings.js'],
        components: ['src/components/admin/SystemSettings.js']
      }
    };

    this.analyzeFeatureSet('Admin', adminFeatures);
  }

  // Analyze website functionality
  analyzeWebsiteFunctionality() {
    log('\n🌐 Analyzing Website Functionality...', 'bright');
    
    const websiteFeatures = {
      'User Registration': {
        files: ['src/website/pages/register.js', 'src/pages/website/register.js'],
        apis: ['src/pages/api/users/register.js', 'src/pages/api/users/verify-otp.js'],
        components: ['src/components/registration/RegistrationForm.js']
      },
      'User Authentication': {
        files: ['src/website/pages/login.js', 'src/pages/website/login.js'],
        apis: ['src/pages/api/auth/login.js', 'src/pages/api/auth/mock-login.js'],
        components: ['src/components/auth/LoginForm.js']
      },
      'User Dashboard': {
        files: ['src/pages/website/dashboard.js'],
        apis: ['src/pages/api/user/dashboard.js'],
        components: ['src/components/dashboard/UserDashboard.js']
      },
      'Profile Management': {
        files: ['src/website/pages/profile/dashboard.js', 'src/pages/website/profile.js'],
        apis: ['src/pages/api/user/profile.js'],
        components: ['src/components/profile/ProfileEditor.js']
      },
      'Matching System': {
        files: ['src/website/pages/matches.js'],
        apis: ['src/pages/api/matches/categorized.js', 'src/pages/api/matching/index.js'],
        components: ['src/components/enhanced/EnhancedMatchDashboard.js']
      },
      'Search Functionality': {
        files: ['src/website/pages/search.js', 'src/website/pages/search-new.js'],
        apis: ['src/pages/api/search/profiles.js'],
        components: ['src/components/search/AdvancedSearch.js']
      },
      'Messaging System': {
        files: ['src/website/pages/messages/index.js'],
        apis: ['src/pages/api/messaging/conversations.js'],
        components: ['src/components/messaging/ChatInterface.js']
      },
      'Premium Features': {
        files: ['src/pages/premium-plans.js'],
        apis: ['src/pages/api/payments/plans.js'],
        components: ['src/components/premium/PremiumPlans.js']
      }
    };

    this.analyzeFeatureSet('Website', websiteFeatures);
  }

  // Analyze backend functionality
  analyzeBackendFunctionality() {
    log('\n🔧 Analyzing Backend Functionality...', 'bright');
    
    const backendPath = path.join(process.cwd(), '../vaivahik-backend');
    if (!fs.existsSync(backendPath)) {
      log('  ⚠️  Backend directory not found', 'yellow');
      return;
    }

    const backendFeatures = {
      'Authentication Routes': {
        files: ['src/routes/auth.routes.js'],
        controllers: ['src/controllers/auth.controller.js'],
        middleware: ['src/middleware/auth.middleware.js']
      },
      'User Management Routes': {
        files: ['src/routes/user.routes.js'],
        controllers: ['src/controllers/user.controller.js'],
        models: ['src/models/User.js']
      },
      'Admin Routes': {
        files: ['src/routes/admin.routes.js'],
        controllers: ['src/controllers/admin.controller.js'],
        middleware: ['src/middleware/admin.middleware.js']
      },
      'Matching Algorithm': {
        files: ['src/services/matchingService.js'],
        controllers: ['src/controllers/matching.controller.js'],
        models: ['src/models/Match.js']
      }
    };

    this.analyzeBackendFeatureSet(backendFeatures, backendPath);
  }

  analyzeFeatureSet(type, features) {
    Object.entries(features).forEach(([featureName, paths]) => {
      const analysis = this.analyzeFeature(featureName, paths);
      this.results[type.toLowerCase()].functionality[featureName] = analysis;
      
      this.results.summary.totalFeatures++;
      if (analysis.status === 'implemented') {
        this.results.summary.implementedFeatures++;
      } else if (analysis.status === 'partial') {
        this.results.summary.partialFeatures++;
      } else {
        this.results.summary.missingFeatures++;
      }
      
      const statusColor = analysis.status === 'implemented' ? 'green' : 
                         analysis.status === 'partial' ? 'yellow' : 'red';
      const statusIcon = analysis.status === 'implemented' ? '✅' : 
                        analysis.status === 'partial' ? '⚠️' : '❌';
      
      log(`  ${statusIcon} ${featureName} - ${analysis.status}`, statusColor);
      
      if (analysis.details.length > 0) {
        analysis.details.forEach(detail => {
          log(`    ${detail}`, 'white');
        });
      }
    });
  }

  analyzeBackendFeatureSet(features, backendPath) {
    Object.entries(features).forEach(([featureName, paths]) => {
      const analysis = this.analyzeBackendFeature(featureName, paths, backendPath);
      this.results.backend.functionality[featureName] = analysis;
      
      this.results.summary.totalFeatures++;
      if (analysis.status === 'implemented') {
        this.results.summary.implementedFeatures++;
      } else if (analysis.status === 'partial') {
        this.results.summary.partialFeatures++;
      } else {
        this.results.summary.missingFeatures++;
      }
      
      const statusColor = analysis.status === 'implemented' ? 'green' : 
                         analysis.status === 'partial' ? 'yellow' : 'red';
      const statusIcon = analysis.status === 'implemented' ? '✅' : 
                        analysis.status === 'partial' ? '⚠️' : '❌';
      
      log(`  ${statusIcon} ${featureName} - ${analysis.status}`, statusColor);
    });
  }

  analyzeFeature(featureName, paths) {
    const analysis = {
      status: 'missing',
      details: [],
      files: {
        found: [],
        missing: []
      }
    };

    let foundFiles = 0;
    let totalFiles = 0;

    // Check all file types
    Object.entries(paths).forEach(([type, filePaths]) => {
      filePaths.forEach(filePath => {
        totalFiles++;
        const fullPath = path.join(process.cwd(), filePath);
        
        if (fs.existsSync(fullPath)) {
          foundFiles++;
          analysis.files.found.push(filePath);
          
          // Analyze file content for completeness
          const content = fs.readFileSync(fullPath, 'utf8');
          const complexity = this.analyzeFileComplexity(content, type);
          analysis.details.push(`${filePath} - ${complexity}`);
        } else {
          analysis.files.missing.push(filePath);
        }
      });
    });

    // Determine status
    if (foundFiles === totalFiles && foundFiles > 0) {
      analysis.status = 'implemented';
    } else if (foundFiles > 0) {
      analysis.status = 'partial';
    } else {
      analysis.status = 'missing';
    }

    return analysis;
  }

  analyzeBackendFeature(featureName, paths, backendPath) {
    const analysis = {
      status: 'missing',
      files: {
        found: [],
        missing: []
      }
    };

    let foundFiles = 0;
    let totalFiles = 0;

    Object.entries(paths).forEach(([type, filePaths]) => {
      filePaths.forEach(filePath => {
        totalFiles++;
        const fullPath = path.join(backendPath, filePath);
        
        if (fs.existsSync(fullPath)) {
          foundFiles++;
          analysis.files.found.push(filePath);
        } else {
          analysis.files.missing.push(filePath);
        }
      });
    });

    // Determine status
    if (foundFiles === totalFiles && foundFiles > 0) {
      analysis.status = 'implemented';
    } else if (foundFiles > 0) {
      analysis.status = 'partial';
    } else {
      analysis.status = 'missing';
    }

    return analysis;
  }

  analyzeFileComplexity(content, fileType) {
    const lines = content.split('\n').length;
    const hasExports = content.includes('export') || content.includes('module.exports');
    const hasImports = content.includes('import') || content.includes('require');
    const hasFunctions = content.includes('function') || content.includes('=>');
    
    if (lines < 50) {
      return 'Basic implementation';
    } else if (lines < 200) {
      return 'Moderate implementation';
    } else {
      return 'Comprehensive implementation';
    }
  }

  // Check for specific functionality patterns
  checkSpecificFunctionality() {
    log('\n🔍 Checking Specific Functionality Patterns...', 'bright');
    
    const functionalityChecks = {
      'Form Validation': {
        pattern: /validation|validate|yup|joi/i,
        files: ['src/utils/validation.js', 'src/components/forms/']
      },
      'Error Handling': {
        pattern: /try.*catch|error.*handler|toast.*error/i,
        files: ['src/utils/errorHandler.js', 'src/contexts/ToastContext.js']
      },
      'Authentication Context': {
        pattern: /AuthContext|useAuth|authentication/i,
        files: ['src/contexts/AuthContext.js']
      },
      'API Integration': {
        pattern: /axios|fetch|api.*call/i,
        files: ['src/services/', 'src/utils/api.js']
      },
      'State Management': {
        pattern: /useState|useReducer|context|redux/i,
        files: ['src/contexts/', 'src/hooks/']
      }
    };

    Object.entries(functionalityChecks).forEach(([name, check]) => {
      const found = this.searchForPattern(check.pattern, check.files);
      const status = found ? '✅' : '❌';
      const color = found ? 'green' : 'red';
      
      log(`  ${status} ${name}`, color);
    });
  }

  searchForPattern(pattern, searchPaths) {
    for (const searchPath of searchPaths) {
      const fullPath = path.join(process.cwd(), searchPath);
      
      if (fs.existsSync(fullPath)) {
        const stats = fs.statSync(fullPath);
        
        if (stats.isFile()) {
          const content = fs.readFileSync(fullPath, 'utf8');
          if (pattern.test(content)) {
            return true;
          }
        } else if (stats.isDirectory()) {
          const files = fs.readdirSync(fullPath);
          for (const file of files) {
            const filePath = path.join(fullPath, file);
            if (fs.statSync(filePath).isFile() && file.endsWith('.js')) {
              const content = fs.readFileSync(filePath, 'utf8');
              if (pattern.test(content)) {
                return true;
              }
            }
          }
        }
      }
    }
    return false;
  }

  async runAudit() {
    log('🔍 Starting Detailed Functionality Audit', 'bright');
    log('=' .repeat(50), 'cyan');
    
    this.analyzeAdminFunctionality();
    this.analyzeWebsiteFunctionality();
    this.analyzeBackendFunctionality();
    this.checkSpecificFunctionality();
    
    this.displaySummary();
    this.saveReport();
  }

  displaySummary() {
    const { summary } = this.results;
    const implementationRate = ((summary.implementedFeatures / summary.totalFeatures) * 100).toFixed(1);
    
    log('\n📊 DETAILED AUDIT SUMMARY', 'bright');
    log('=' .repeat(40), 'cyan');
    log(`Total Features Analyzed: ${summary.totalFeatures}`, 'white');
    log(`✅ Fully Implemented: ${summary.implementedFeatures}`, 'green');
    log(`⚠️  Partially Implemented: ${summary.partialFeatures}`, 'yellow');
    log(`❌ Missing: ${summary.missingFeatures}`, 'red');
    log(`📈 Implementation Rate: ${implementationRate}%`, implementationRate > 80 ? 'green' : 'yellow');
    
    // Feature breakdown
    log('\n📋 Feature Breakdown:', 'bright');
    
    ['admin', 'website', 'backend'].forEach(section => {
      const sectionData = this.results[section];
      const features = Object.keys(sectionData.functionality);
      const implemented = Object.values(sectionData.functionality)
        .filter(f => f.status === 'implemented').length;
      
      log(`  ${section.toUpperCase()}: ${implemented}/${features.length} features`, 
          implemented === features.length ? 'green' : 'yellow');
    });
    
    // Recommendations
    log('\n🚀 Recommendations:', 'bright');
    if (summary.missingFeatures > 0) {
      log('1. Prioritize implementing missing critical features', 'white');
    }
    if (summary.partialFeatures > 0) {
      log('2. Complete partially implemented features', 'white');
    }
    log('3. Add comprehensive testing for implemented features', 'white');
    log('4. Set up monitoring for production deployment', 'white');
  }

  saveReport() {
    const reportPath = path.join(process.cwd(), 'test-reports', 'detailed-functionality-audit.json');
    
    // Create directory if it doesn't exist
    const reportDir = path.dirname(reportPath);
    if (!fs.existsSync(reportDir)) {
      fs.mkdirSync(reportDir, { recursive: true });
    }
    
    const report = {
      timestamp: new Date().toISOString(),
      auditType: 'Detailed Functionality Audit',
      results: this.results,
      recommendations: this.generateDetailedRecommendations()
    };
    
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    log(`\n📄 Detailed audit report saved to: ${reportPath}`, 'cyan');
  }

  generateDetailedRecommendations() {
    const recommendations = [];
    
    // Analyze missing features
    const missingFeatures = [];
    ['admin', 'website', 'backend'].forEach(section => {
      Object.entries(this.results[section].functionality).forEach(([name, feature]) => {
        if (feature.status === 'missing') {
          missingFeatures.push(`${section}: ${name}`);
        }
      });
    });
    
    if (missingFeatures.length > 0) {
      recommendations.push({
        priority: 'high',
        category: 'Missing Features',
        items: missingFeatures
      });
    }
    
    // Analyze partial features
    const partialFeatures = [];
    ['admin', 'website', 'backend'].forEach(section => {
      Object.entries(this.results[section].functionality).forEach(([name, feature]) => {
        if (feature.status === 'partial') {
          partialFeatures.push(`${section}: ${name}`);
        }
      });
    });
    
    if (partialFeatures.length > 0) {
      recommendations.push({
        priority: 'medium',
        category: 'Partial Features',
        items: partialFeatures
      });
    }
    
    return recommendations;
  }
}

// Main execution
async function main() {
  const auditor = new DetailedAuditor();
  
  try {
    await auditor.runAudit();
  } catch (error) {
    log(`💥 Detailed audit failed: ${error.message}`, 'red');
    console.error(error);
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { DetailedAuditor };
