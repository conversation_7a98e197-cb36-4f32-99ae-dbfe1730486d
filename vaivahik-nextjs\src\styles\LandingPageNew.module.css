/* Complete Landing Page Styles - CSS Module Compatible */

/* CSS Variables */
.page {
    /* Core Colors */
    --primary-color: #FF5F6D; /* Coral Pink */
    --primary-light: #FFC371; /* Light Orange */
    --secondary-color: #8A2BE2; /* Blue Violet */
    --secondary-light: #9370DB; /* Medium Purple */
    --accent-color: #FFD700; /* Gold */

    /* Backgrounds & Text */
    --dark-color: #2D3047; /* Deep Blue/Gray */
    --light-color: #F8F9FA; /* Very Light Gray */
    --light-color-alt: #F0F2F5; /* Slightly different light gray for alternation */
    --white: #FFFFFF;
    --text-color-dark: #333;
    --text-color-medium: #555;
    --text-color-light: #F0F0F0;
    --text-color-light-muted: #B0B0B0;

    /* Gradients */
    --primary-gradient: linear-gradient(135deg, #FF5F6D 0%, #FFC371 100%);
    --secondary-gradient: linear-gradient(135deg, #8A2BE2 0%, #9370DB 100%);
    --subtle-white-gradient: linear-gradient(180deg, var(--white) 0%, #fcfdff 100%);
    --subtle-light-gradient: linear-gradient(180deg, var(--light-color) 0%, #f0f2f5 100%);

    /* Shadows & Effects */
    --shadow-soft: 0 5px 15px rgba(0,0,0,0.05);
    --shadow-medium: 0 10px 25px rgba(0,0,0,0.1);
    --shadow-hard: 0 15px 35px rgba(0,0,0,0.15);
    --transition-smooth: all 0.3s ease;
    --border-radius-medium: 20px;
    --border-radius-large: 25px;

    font-family: 'Montserrat', sans-serif;
    color: var(--text-color-dark);
    line-height: 1.6;
    overflow-x: hidden;
    background-color: var(--white);
}

.container {
    width: 90%;
    max-width: 1400px;
    margin: 0 auto;
}

/* Button Styles */
.btn {
    display: inline-block;
    padding: 12px 30px;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-smooth), transform 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    border: none;
    font-size: 16px;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.btnPrimary { background: var(--primary-gradient); color: var(--white); box-shadow: 0 5px 15px rgba(255, 95, 109, 0.4); }
.btnSecondary { background: var(--secondary-gradient); color: var(--white); box-shadow: 0 5px 15px rgba(138, 43, 226, 0.4); }
.btnOutline { background: transparent; color: var(--primary-color); border: 2px solid var(--primary-color); }
.btnOutlineSecondary { background: transparent; color: var(--secondary-color); border: 2px solid var(--secondary-color); }

.btn:hover, .btn:focus { transform: translateY(-5px); box-shadow: var(--shadow-hard); filter: brightness(1.1); outline: none; }
.btnOutline:hover, .btnOutline:focus { background: var(--primary-color); color: var(--white); }
.btnOutlineSecondary:hover, .btnOutlineSecondary:focus { background: var(--secondary-color); color: var(--white); }
.btnPrimary:hover, .btnPrimary:focus { box-shadow: 0 8px 25px rgba(255, 95, 109, 0.6); }
.btnSecondary:hover, .btnSecondary:focus { box-shadow: 0 8px 25px rgba(138, 43, 226, 0.6); }

.btn::after {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s ease-out, height 0.6s ease-out;
    z-index: -1;
    opacity: 0;
}

.btn:hover::after { width: 300px; height: 300px; opacity: 1; }
.btnSmall { padding: 10px 25px; font-size: 15px; }

/* Section Base Styles - Only local classes allowed in CSS modules */

.sectionContentWrapper {
    position: relative;
    z-index: 2;
}

/* Section Title Styles */
.sectionTitle { text-align: center; margin-bottom: 60px; position: relative; }
.sectionTitle h2 {
    font-size: 42px;
    margin-bottom: 20px;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    display: inline-block;
}

.sectionTitle .subtitle {
    font-size: 18px;
    color: var(--text-color-medium);
    max-width: 700px;
    margin: 0 auto 20px;
    position: relative;
    opacity: 0.9;
}

.sectionTitle::after {
    content: '';
    width: 80px;
    height: 3px;
    background: var(--primary-gradient);
    position: absolute;
    bottom: 0px;
    left: 50%;
    transform: translateX(-50%);
    border-radius: 2px;
}

/* Header */
.mainHeader {
    background: rgba(255, 255, 255, 0.95);
    padding: 15px 0;
    position: fixed;
    width: 100%;
    z-index: 1000;
    box-shadow: 0 2px 20px rgba(0,0,0,0.05);
    transition: var(--transition-smooth);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
}

.mainHeader.scrolled {
    padding: 10px 0;
    background: rgba(255, 255, 255, 0.98);
    box-shadow: var(--shadow-medium);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.headerContainer { display: flex; justify-content: space-between; align-items: center; }

.logo {
    font-family: 'Playfair Display', serif;
    font-size: 28px;
    font-weight: 700;
    display: flex;
    align-items: center;
    text-decoration: none;
    transition: transform 0.3s ease;
}

.logo:hover { transform: scale(1.05); }

.logoText {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

.logoSymbol {
    margin-right: 10px;
    font-size: 32px;
    color: var(--primary-color);
    transition: transform 0.5s ease;
}

.logo:hover .logoSymbol { transform: rotate(360deg); }
.logoText span { color: var(--secondary-color); font-weight: 800; }

.mainNav ul { display: flex; list-style: none; align-items: center; }
.mainNav ul li { margin-left: 30px; }

.mainNav ul li a {
    color: var(--dark-color);
    text-decoration: none;
    font-weight: 600;
    transition: var(--transition-smooth);
    position: relative;
    padding: 5px 0;
    letter-spacing: 0.5px;
}

.mainNav ul li a::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    background: var(--primary-gradient);
    bottom: -2px;
    left: 50%;
    transform: translateX(-50%);
    transition: width 0.3s ease;
    border-radius: 1px;
}

.mainNav ul li a:hover, .mainNav ul li a:focus { color: var(--primary-color); outline: none; }
.mainNav ul li a:hover::after, .mainNav ul li a:focus::after { width: 100%; }

/* Ensure navigation button text stays visible on hover */
.mainNav ul li a.btn {
    position: relative;
    z-index: 2;
    background: linear-gradient(135deg, #FF6B6B 0%, #4ECDC4 100%);
    color: white !important;
    padding: 12px 24px;
    border-radius: 25px;
    font-weight: 600;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
    transition: all 0.3s ease;
    border: none;
    text-decoration: none;
}

.mainNav ul li a.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
    background: linear-gradient(135deg, #FF5252 0%, #26C6DA 100%);
}

.mainNav ul li a.btn * {
    position: relative;
    z-index: 3;
    color: white !important;
}

.activeLink { color: var(--primary-color) !important; }
.activeLink::after { width: 100% !important; }

.hamburger {
    display: none;
    cursor: pointer;
    z-index: 1001;
    padding: 5px;
    background: transparent;
    border: none;
}

.hamburger div {
    width: 25px;
    height: 3px;
    background: var(--primary-color);
    margin: 5px;
    transition: all 0.4s ease;
    border-radius: 3px;
}

.hamburger.active .line1 { transform: rotate(-45deg) translate(-5px, 6px); }
.hamburger.active .line2 { opacity: 0; transform: translateX(-20px); }
.hamburger.active .line3 { transform: rotate(45deg) translate(-5px, -6px); }

/* Hero Section */
.hero {
    background: url('https://picsum.photos/seed/vaivahikHeroPro/1920/1080') center/cover no-repeat;
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    color: var(--white);
    padding: 0;
}

.hero::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, rgba(45, 48, 71, 0.88) 0%, rgba(255, 95, 109, 0.78) 100%);
    z-index: 1;
}

.heroContent {
    position: relative;
    z-index: 2;
    max-width: 750px;
    animation: fadeInUp 1s ease;
}

.hero h1 {
    font-size: 58px;
    font-weight: 700;
    margin-bottom: 25px;
    line-height: 1.25;
    text-shadow: 0 3px 12px rgba(0,0,0,0.3);
    color: var(--white);
    font-family: 'Playfair Display', serif;
}

.hero p {
    font-size: 21px;
    margin-bottom: 35px;
    text-shadow: 0 2px 7px rgba(0,0,0,0.25);
    opacity: 0.95;
    color: rgba(255, 255, 255, 0.95);
}

.heroButtons {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-top: 30px;
}

.floatingHearts { position: absolute; inset: 0; z-index: 1; overflow: hidden; pointer-events: none; }

.heart {
    position: absolute;
    width: 30px;
    height: 30px;
    background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF5F6D'%3E%3Cpath d='M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z'/%3E%3C/svg%3E") no-repeat center center;
    background-size: contain;
    opacity: 0.6;
    animation: floating 15s linear infinite;
    will-change: transform, opacity;
}

@keyframes floating {
    0% { transform: translateY(100vh) scale(0.5) rotate(0deg); opacity: 0; }
    10% { opacity: 0.8; }
    90% { opacity: 0.8; }
    100% { transform: translateY(-100px) scale(1.2) rotate(360deg); opacity: 0; }
}

@keyframes fadeInUp {
    from { opacity: 0; transform: translateY(30px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Section Base Styles */
.section {
    padding: 100px 0;
    position: relative;
    overflow: hidden;
}

.sectionContentWrapper {
    position: relative;
    z-index: 2;
}

/* Section Title Styles */
.sectionTitle {
    text-align: center;
    margin-bottom: 60px;
    position: relative;
}

.sectionTitle h2 {
    font-size: 42px;
    margin-bottom: 20px;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    display: inline-block;
}

.sectionTitle .subtitle {
    font-size: 18px;
    color: var(--text-color-medium);
    max-width: 700px;
    margin: 0 auto 20px;
    position: relative;
    opacity: 0.9;
}

.sectionTitle::after {
    content: '';
    width: 80px;
    height: 3px;
    background: var(--primary-gradient);
    position: absolute;
    bottom: 0px;
    left: 50%;
    transform: translateX(-50%);
    border-radius: 2px;
}

/* Button Styles */
.btn {
    display: inline-block;
    padding: 12px 30px;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-smooth), transform 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    border: none;
    font-size: 16px;
    text-align: center;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.btnPrimary {
    background: var(--primary-gradient);
    color: var(--white);
    box-shadow: 0 5px 15px rgba(255, 95, 109, 0.4);
}

.btnSecondary {
    background: var(--secondary-gradient);
    color: var(--white);
    box-shadow: 0 5px 15px rgba(138, 43, 226, 0.4);
}

.btnOutline {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btnOutlineSecondary {
    background: transparent;
    color: var(--secondary-color);
    border: 2px solid var(--secondary-color);
}

.btn:hover, .btn:focus {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hard);
    filter: brightness(1.1);
    outline: none;
}

.btnOutline:hover, .btnOutline:focus {
    background: var(--primary-color);
    color: var(--white);
}

.btnOutlineSecondary:hover, .btnOutlineSecondary:focus {
    background: var(--secondary-color);
    color: var(--white);
}

.btnPrimary:hover, .btnPrimary:focus {
    box-shadow: 0 8px 25px rgba(255, 95, 109, 0.6);
}

.btnSecondary:hover, .btnSecondary:focus {
    box-shadow: 0 8px 25px rgba(138, 43, 226, 0.6);
}

.btn::after {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s ease-out, height 0.6s ease-out;
    z-index: -1;
    opacity: 0;
}

.btn:hover::after {
    width: 300px;
    height: 300px;
    opacity: 1;
}

.btnSmall {
    padding: 10px 25px;
    font-size: 15px;
}

.btnLarge {
    padding: 18px 40px;
    font-size: 18px;
    font-weight: 700;
    min-width: 200px;
}

/* Shape Divider Base Style */
.shapeDivider {
    position: absolute;
    left: 0;
    width: 100%;
    overflow: hidden;
    line-height: 0;
    z-index: 1;
}

.shapeDivider svg {
    position: relative;
    display: block;
    width: calc(100% + 1.3px);
    height: auto;
}

.shapeDivider.top { top: -1px; }
.shapeDivider.bottom { bottom: -1px; transform: rotate(180deg); }

.shapeFill { fill: var(--white); }

/* Features Section - Enhanced with Premium Design */
.features {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
    overflow: hidden;
    padding: 120px 0;
}

.features::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
    animation: float 20s ease-in-out infinite;
}

/* Decorative blobs for features section */
.features::before {
    content: '';
    position: absolute;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(255, 95, 109, 0.07) 0%, rgba(255, 195, 113, 0.1) 100%);
    top: -120px;
    right: -100px;
    z-index: 0;
    transform: rotate(25deg);
    animation: blobAnim1 20s infinite alternate ease-in-out;
}

.features::after {
    content: '';
    position: absolute;
    width: 400px;
    height: 400px;
    border-radius: 45%;
    background: linear-gradient(135deg, rgba(138, 43, 226, 0.06) 0%, rgba(147, 112, 219, 0.09) 100%);
    bottom: -180px;
    left: -150px;
    z-index: 0;
    transform: rotate(-35deg);
    animation: blobAnim2 25s infinite alternate ease-in-out;
}

@keyframes blobAnim1 {
    0% { transform: scale(1) translate(0,0) rotate(25deg); }
    100% { transform: scale(1.1) translate(20px, -15px) rotate(35deg); }
}

@keyframes blobAnim2 {
    0% { transform: scale(1) translate(0,0) rotate(-35deg); }
    100% { transform: scale(1.1) translate(-15px, 20px) rotate(-25deg); }
}

.featuresGrid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 30px;
    position: relative;
    z-index: 2;
    margin-top: 60px;
}

/* Enhanced Section Title for Features */
.features .sectionTitle h2 {
    color: white;
    background: linear-gradient(135deg, #ffffff 0%, #f0f0f0 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.features .sectionTitle .subtitle {
    color: rgba(255, 255, 255, 0.9);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Floating Animation */
@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-10px) rotate(2deg); }
}

.featureCard {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    padding: 40px 30px;
    border-radius: 24px;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.2);
    height: 100%;
    display: flex;
    flex-direction: column;
    text-align: center;
    color: white;
}

.featureCard::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.6s ease;
}

.featureCard:hover {
    transform: translateY(-12px) scale(1.02);
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.3);
}

.featureCard:hover::before {
    left: 100%;
}

.featureVisualWrapper {
    width: 80px;
    height: 80px;
    margin: 0 auto 30px auto;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #ff6b6b 0%, #feca57 100%);
    border-radius: 20px;
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.featureVisualWrapper::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
    border-radius: 20px;
}

.featureVisual {
    font-size: 36px;
    color: white;
    transition: all 0.4s ease;
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));
    position: relative;
    z-index: 1;
}

.featureCard:hover .featureVisualWrapper {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 12px 35px rgba(255, 107, 107, 0.4);
}

.featureCard:hover .featureVisual {
    transform: scale(1.1);
    filter: drop-shadow(0 4px 8px rgba(0,0,0,0.3));
}

.featureCard:nth-child(even) .featureVisualWrapper {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.featureCard:nth-child(even):hover .featureVisualWrapper {
    box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
}

.featureCard:nth-child(3n) .featureVisualWrapper {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    box-shadow: 0 8px 25px rgba(240, 147, 251, 0.3);
}

.featureCard:nth-child(3n):hover .featureVisualWrapper {
    box-shadow: 0 12px 35px rgba(240, 147, 251, 0.4);
}

.featureCard h3 { font-size: 24px; margin-bottom: 15px; color: var(--dark-color); }

.featureCard p {
    color: var(--text-color-medium);
    font-size: 16px;
    line-height: 1.7;
    flex-grow: 1;
    margin-bottom: 20px;
}

.securityPrivacyInfo {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 15px;
    margin-top: auto;
    font-size: 14px;
    color: var(--text-color-medium);
}

.securityPrivacyInfo i { color: var(--primary-color); }

/* How It Works Section */
.howItWorks {
    background-color: var(--light-color);
}

.howItWorks .shapeDivider.top svg path {
    fill: var(--white);
}

.processTimeline {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
    position: relative;
    margin-top: 80px;
    z-index: 2;
}

.processTimeline::before {
    content: '';
    position: absolute;
    width: calc(100% - 200px);
    max-width: 1000px;
    height: 4px;
    background: var(--primary-gradient);
    opacity:0.7;
    top: 58px;
    left: 50%;
    transform: translateX(-50%);
    z-index: -1;
    display: none;
    border-radius: 2px;
}

.processStep {
    flex: 1;
    min-width: 250px;
    text-align: center;
    padding: 0 20px;
    position: relative;
    margin-bottom: 40px;
}

.stepVisual {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 25px;
}

.stepIconWrapper {
    margin-bottom: 10px;
}

.stepIcon {
    font-size: 32px;
    color: var(--primary-color);
    transition: transform 0.3s ease;
}

.processStep:hover .stepIcon {
    transform: scale(1.2) rotate(-5deg);
}

.stepNumberWrapper {
    display: flex;
    align-items: center;
    justify-content: center;
}

.stepNumber {
    background: var(--primary-gradient);
    color: var(--white);
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28px;
    font-weight: 700;
    position: relative;
    z-index: 2;
    box-shadow: 0 8px 20px rgba(255, 95, 109, 0.3);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    border: 3px solid var(--white);
}

.processStep:hover .stepNumber {
    transform: scale(1.1) rotate(3deg);
    box-shadow: 0 15px 30px rgba(255, 95, 109, 0.4);
}

.processStep h3 { color: var(--dark-color); margin-bottom: 10px; }
.processStep p { color: var(--text-color-medium); font-size: 15px; }

/* Testimonials Section - Enhanced Success Stories */
.testimonials {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: var(--white);
    position: relative;
    padding-top: 120px;
    padding-bottom: 120px;
    overflow: hidden;
}

.testimonials::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="hearts" width="100" height="100" patternUnits="userSpaceOnUse"><path d="M50 25c-5-10-20-10-20 0 0 10 20 20 20 20s20-10 20-20c0-10-15-10-20 0z" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23hearts)"/></svg>');
    opacity: 0.3;
    animation: float 30s ease-in-out infinite;
}

.testimonials .shapeDivider.top svg path { fill: var(--light-color); }
.testimonials .shapeDivider.bottom svg path { fill: var(--white); }

.testimonials::before {
    content: '';
    position: absolute;
    inset: 0;
    background: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23FFFFFF' fill-opacity='0.07' fill-rule='evenodd'/%3E%3C/svg%3E");
    z-index: 0;
}

.testimonials .sectionTitle h2 {
    background: none;
    -webkit-text-fill-color: var(--white);
    text-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

.testimonials .sectionTitle .subtitle { color: rgba(255,255,255,0.9); }
.testimonials .sectionTitle::after { background: var(--white); height: 4px; bottom: 5px; }

.testimonialSlider {
    position: relative;
    z-index: 1;
    padding: 40px 0;
    max-width: 900px;
    margin: 0 auto;
    overflow: visible;
}

.testimonialCard {
    background: rgba(255,255,255,0.15);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    padding: 45px;
    border-radius: 24px;
    border: 1px solid rgba(255,255,255,0.3);
    box-shadow:
        0 20px 60px rgba(0,0,0,0.15),
        inset 0 1px 0 rgba(255,255,255,0.2);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    margin: 0 10px;
    height: auto;
    min-height: 320px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.testimonialCard:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow:
        0 25px 80px rgba(0,0,0,0.2),
        inset 0 1px 0 rgba(255,255,255,0.3);
    border-color: rgba(255,255,255,0.4);
}

.testimonialCard::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    transition: left 0.6s ease;
}

.testimonialCard:hover::after {
    left: 100%;
}

.testimonialCard::before {
    content: '\201C';
    font-family: Georgia, serif;
    position: absolute;
    top: 25px;
    left: 25px;
    font-size: 130px;
    color: rgba(255,255,255,0.1);
    line-height: 1;
    z-index: 0;
    transition: transform 0.4s ease;
}

.testimonialText {
    font-style: italic;
    margin-bottom: 35px;
    font-size: 19px;
    line-height: 1.85;
    position: relative;
    z-index: 1;
    flex-grow: 1;
    color: rgba(255,255,255,0.95);
}

.testimonialAuthor {
    display: flex;
    align-items: center;
    margin-top: auto;
    position: relative;
    z-index: 2;
}

.authorImage {
    width: 75px;
    height: 75px;
    border-radius: 50%;
    margin-right: 20px;
    background-color: rgba(255,255,255,0.2);
    background-size: cover;
    background-position: center;
    border: 4px solid rgba(255,255,255,0.35);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    transition: all 0.3s ease;
}

.testimonialCard:hover .authorImage {
    transform: scale(1.1);
    border-color: rgba(255,255,255,0.5);
    box-shadow: 0 12px 35px rgba(0,0,0,0.2);
}

.authorInfo h4 {
    font-size: 21px;
    margin-bottom: 8px;
    color: var(--white);
    font-weight: 600;
}

.authorInfo p {
    font-size: 16px;
    opacity: 0.85;
    margin-bottom: 8px;
}

/* Rating Stars */
.testimonialRating {
    display: flex;
    gap: 4px;
    margin-bottom: 12px;
}

.testimonialRating .star {
    color: #FFD700;
    font-size: 18px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    animation: starTwinkle 2s ease-in-out infinite;
}

.testimonialRating .star:nth-child(2) { animation-delay: 0.2s; }
.testimonialRating .star:nth-child(3) { animation-delay: 0.4s; }
.testimonialRating .star:nth-child(4) { animation-delay: 0.6s; }
.testimonialRating .star:nth-child(5) { animation-delay: 0.8s; }

@keyframes starTwinkle {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.7; transform: scale(1.1); }
}

/* Success Story Badge */
.successBadge {
    position: absolute;
    top: 20px;
    right: 20px;
    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
    z-index: 3;
}

/* Success Stories Section */
.successStories { background-color: var(--white); }
.successStories .shapeDivider.bottom svg path { fill: var(--light-color-alt); }

.successGrid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 35px;
    position: relative;
    z-index: 2;
}

.successCard {
    background: var(--white);
    border-radius: var(--border-radius-medium);
    overflow: hidden;
    box-shadow: var(--shadow-medium);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    display: flex;
    flex-direction: column;
    border: 1px solid rgba(0,0,0,0.05);
}

.successCard:hover {
    transform: translateY(-12px) scale(1.03);
    box-shadow: var(--shadow-hard);
}

.successImage {
    height: 260px;
    background-size: cover;
    background-position: center;
    position: relative;
    overflow: hidden;
}

.successImage::before {
    content: '';
    position: absolute;
    inset: 0;
    background: inherit;
    background-size: inherit;
    background-position: inherit;
    transition: transform 0.5s ease;
}

.successCard:hover .successImage::before { transform: scale(1.1); }

.successOverlay {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 50%;
    background: linear-gradient(to top, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0) 100%);
    display: flex;
    align-items: flex-end;
    padding: 20px;
    box-sizing: border-box;
    z-index: 1;
    transition: height 0.4s ease;
}

.successCard:hover .successOverlay { height: 60%; }

.successNames {
    color: var(--white);
    font-size: 24px;
    font-weight: 700;
    font-family: 'Playfair Display', serif;
    text-shadow: 1px 1px 3px rgba(0,0,0,0.5);
}

.successContent {
    padding: 30px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.successMeta {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 20px;
    color: #666;
    font-size: 14px;
}

.metaItem { display: flex; align-items: center; }

.metaItem i {
    margin-right: 7px;
    color: var(--primary-color);
    width: 16px;
    text-align: center;
}

.successStory {
    font-size: 16px;
    line-height: 1.75;
    margin-bottom: 25px;
    color: var(--text-color-medium);
    flex-grow: 1;
}

.readMore {
    color: var(--primary-color);
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    transition: var(--transition-smooth);
    margin-top: auto;
    padding: 5px 0;
}

.readMore i { margin-left: 8px; transition: transform 0.3s ease; }

.readMore:hover, .readMore:focus {
    color: var(--secondary-color);
    letter-spacing: 0.5px;
    outline: none;
}

.readMore:hover i, .readMore:focus i { transform: translateX(6px); }

/* As Seen On Section */
.asSeenOn {
    background-color: var(--light-color-alt);
    padding: 80px 0;
    position: relative;
}

.asSeenOn h3 {
    text-align: center;
    font-size: 28px;
    margin-bottom: 50px;
    color: var(--dark-color);
    font-weight: 600;
}

.mediaLogos {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    gap: 40px;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.mediaLogos:hover {
    opacity: 1;
}

.mediaLogos img {
    height: 45px;
    width: auto;
    filter: grayscale(100%);
    transition: all 0.3s ease;
}

.mediaLogos img:hover {
    filter: grayscale(0%);
    transform: scale(1.1);
}

/* Pricing Section - Enhanced with Premium Design */
.pricing {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 120px 0;
    position: relative;
    overflow: hidden;
}

.pricing::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="pricingGrain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23pricingGrain)"/></svg>');
    opacity: 0.2;
    animation: float 25s ease-in-out infinite;
}

.pricing .shapeDivider.top svg path {
    fill: var(--light-color-alt);
}

.pricingTabs {
    display: flex;
    justify-content: center;
    margin-bottom: 60px;
    background: var(--light-color);
    border-radius: 50px;
    padding: 8px;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 60px;
    box-shadow: var(--shadow-soft);
}

.pricingTab {
    flex: 1;
    padding: 15px 25px;
    text-align: center;
    cursor: pointer;
    border-radius: 50px;
    transition: var(--transition-smooth);
    font-weight: 600;
    font-size: 15px;
    color: var(--text-color-medium);
    background: transparent;
    border: none;
}

.pricingTab.active {
    background: var(--primary-gradient);
    color: var(--white);
    box-shadow: 0 5px 15px rgba(255, 95, 109, 0.3);
    transform: translateY(-2px);
}

.pricingGrid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 40px;
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
}

.pricingCard {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: 24px;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    border: 1px solid rgba(255, 255, 255, 0.2);
    display: flex;
    flex-direction: column;
    color: white;
}

.pricingCard::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.6s ease;
}

.pricingCard:hover {
    transform: translateY(-12px) scale(1.02);
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.3);
}

.pricingCard:hover::before {
    left: 100%;
}

.pricingCard.recommended {
    transform: scale(1.05);
    border-color: rgba(255, 215, 0, 0.5);
    box-shadow:
        0 20px 60px rgba(255, 215, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    background: rgba(255, 255, 255, 0.15);
}

.pricingCard.recommended:hover {
    transform: translateY(-12px) scale(1.07);
    box-shadow:
        0 25px 80px rgba(255, 215, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

.popularBadge {
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--primary-gradient);
    color: var(--white);
    padding: 8px 25px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
    z-index: 3;
    box-shadow: 0 5px 15px rgba(255, 95, 109, 0.4);
}

.pricingHeader {
    padding: 40px 30px 30px;
    text-align: center;
    background: rgba(255, 255, 255, 0.05);
    position: relative;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.pricingHeader h3 {
    font-size: 28px;
    margin-bottom: 15px;
    color: white;
    font-weight: 700;
    background: linear-gradient(135deg, #ffffff 0%, #f0f0f0 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

/* Enhanced Section Title for Pricing */
.pricing .sectionTitle h2 {
    color: white;
    background: linear-gradient(135deg, #ffffff 0%, #f0f0f0 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.pricing .sectionTitle .subtitle {
    color: rgba(255, 255, 255, 0.9);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.planDescription {
    color: var(--text-color-medium);
    font-size: 16px;
    margin-bottom: 25px;
    line-height: 1.5;
}

.price {
    font-size: 48px;
    font-weight: 800;
    color: var(--primary-color);
    font-family: 'Playfair Display', serif;
    line-height: 1;
}

.price span {
    font-size: 18px;
    font-weight: 400;
    color: var(--text-color-medium);
    font-family: 'Montserrat', sans-serif;
}

.pricingFeatures {
    padding: 30px;
    list-style: none;
    flex-grow: 1;
}

.pricingFeatures li {
    padding: 12px 0;
    display: flex;
    align-items: center;
    font-size: 16px;
    color: white;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.pricingFeatures li:hover {
    padding-left: 10px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
}

.pricingFeatures li:last-child {
    border-bottom: none;
}

.pricingFeatures li i {
    margin-right: 15px;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    font-size: 12px;
    font-weight: bold;
}

.pricingFeatures li .fa-check {
    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
}

.pricingFeatures li .fa-star {
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(255, 215, 0, 0.3);
}

.pricingFeatures li .fa-times {
    background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(244, 67, 54, 0.3);
}

.pricingFeatures li.disabled {
    opacity: 0.5;
    color: rgba(255, 255, 255, 0.5);
}

.pricingCard .btn {
    margin: 0 30px 30px;
    padding: 15px 30px;
    font-size: 16px;
    font-weight: 600;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.pricingCard .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

.pricingCard .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
}

.pricingCard .btn:hover::before {
    left: 100%;
}

.pricingCard.recommended .btn {
    background: linear-gradient(135deg, #ff6b6b 0%, #feca57 100%);
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
}

.pricingCard.recommended .btn:hover {
    box-shadow: 0 12px 35px rgba(255, 107, 107, 0.4);
}

.pricingCardFooterNote {
    text-align: center;
    margin-top: 40px;
    color: var(--text-color-medium);
    font-size: 14px;
    font-style: italic;
}

/* Blog Preview Section */
.blogPreview {
    background-color: var(--light-color);
    padding: 100px 0;
    position: relative;
}

.blogPreview .shapeDivider.top svg path {
    fill: var(--white);
}

.blogGrid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 40px;
    position: relative;
    z-index: 2;
}

.blogCard {
    background: var(--white);
    border-radius: var(--border-radius-medium);
    overflow: hidden;
    box-shadow: var(--shadow-medium);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    display: flex;
    flex-direction: column;
    border: 1px solid rgba(0,0,0,0.05);
}

.blogCard:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: var(--shadow-hard);
}

.blogCardImage {
    height: 200px;
    background-size: cover;
    background-position: center;
    position: relative;
    overflow: hidden;
}

.blogCardImage::before {
    content: '';
    position: absolute;
    inset: 0;
    background: inherit;
    background-size: inherit;
    background-position: inherit;
    transition: transform 0.5s ease;
}

.blogCard:hover .blogCardImage::before {
    transform: scale(1.1);
}

.blogCardContent {
    padding: 30px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.blogMeta {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;
    font-size: 14px;
    color: var(--text-color-medium);
}

.blogMeta span {
    display: flex;
    align-items: center;
}

.blogMeta i {
    margin-right: 8px;
    color: var(--primary-color);
}

.blogCardContent h3 {
    margin-bottom: 15px;
    font-size: 20px;
    line-height: 1.4;
}

.blogCardContent h3 a {
    color: var(--dark-color);
    text-decoration: none;
    transition: color 0.3s ease;
}

.blogCardContent h3 a:hover {
    color: var(--primary-color);
}

.blogExcerpt {
    color: var(--text-color-medium);
    line-height: 1.6;
    margin-bottom: 20px;
    flex-grow: 1;
}

.blogReadMore {
    color: var(--primary-color);
    font-weight: 600;
    text-decoration: none;
    transition: var(--transition-smooth);
    margin-top: auto;
}

.blogReadMore:hover {
    color: var(--secondary-color);
    letter-spacing: 0.5px;
}

/* CTA Section */
.cta {
    background: var(--primary-gradient);
    color: var(--white);
    text-align: center;
    padding: 100px 0;
    position: relative;
}

.cta .shapeDivider.top svg path {
    fill: var(--light-color);
}

.cta h2 {
    font-size: 42px;
    margin-bottom: 25px;
    font-family: 'Playfair Display', serif;
    text-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

.cta p {
    font-size: 20px;
    margin-bottom: 40px;
    opacity: 0.95;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
}

.cta .btn {
    padding: 18px 40px;
    font-size: 18px;
    font-weight: 600;
}

/* Footer */
.footer {
    background-color: var(--dark-color);
    color: var(--text-color-light);
    padding: 80px 0 40px;
    position: relative;
}

.footer .shapeDivider.top svg path {
    fill: var(--primary-color);
}

.footerContent {
    display: grid;
    grid-template-columns: 1fr 2fr 1fr;
    gap: 40px;
    align-items: start;
    margin-bottom: 40px;
}

.footerLogo {
    color: var(--text-color-light);
}

.footerLinks {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 40px;
    margin: 40px 0;
}

.footerSection h4 {
    color: var(--text-color-light);
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 20px;
    position: relative;
}

.footerSection h4::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 30px;
    height: 2px;
    background: var(--primary-color);
    border-radius: 1px;
}

.footerSection ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footerSection li {
    margin-bottom: 12px;
}

.footerLinks a {
    color: var(--text-color-light-muted);
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 0.95rem;
    display: inline-block;
}

.footerLinks a:hover {
    color: var(--primary-color);
    transform: translateX(5px);
}

.socialLinks {
    display: flex;
    justify-content: flex-end;
    gap: 20px;
}

.socialLinks a {
    width: 45px;
    height: 45px;
    background: rgba(255,255,255,0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-color-light);
    text-decoration: none;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
}

.socialLinks a:hover {
    background: var(--primary-color);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(255, 95, 109, 0.4);
}

.copyright {
    text-align: center;
    padding-top: 30px;
    border-top: 1px solid rgba(255,255,255,0.1);
    color: var(--text-color-light-muted);
    font-size: 14px;
    line-height: 1.6;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .container { width: 95%; }

    .mainHeader .mainNav ul {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: var(--white);
        flex-direction: column;
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.5s ease-in-out;
        box-shadow: var(--shadow-hard);
        border-bottom-left-radius: 10px;
        border-bottom-right-radius: 10px;
    }

    .mainHeader .mainNav.active ul {
        max-height: 60vh;
        padding-bottom: 15px;
    }

    .mainHeader .mainNav ul li {
        margin-left: 0;
        width: 100%;
        text-align: center;
    }

    .mainHeader .mainNav ul li a {
        display: block;
        padding: 18px;
        border-bottom: 1px solid #eee;
    }

    .mainHeader .mainNav ul li:last-child a {
        border-bottom: none;
    }

    .mainHeader .mainNav ul li a.btn {
        margin: 15px auto;
        display: inline-block;
        width: auto;
        position: relative;
        z-index: 2;
        text-align: center;
        padding: 14px 28px;
        font-size: 15px;
        border-radius: 30px;
        background: linear-gradient(135deg, #FF6B6B 0%, #4ECDC4 100%);
        box-shadow: 0 6px 20px rgba(255, 107, 107, 0.35);
    }

    .mainHeader .mainNav ul li a.btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 30px rgba(255, 107, 107, 0.45);
    }

    .hamburger {
        display: block;
    }

    .hero h1 {
        font-size: 44px;
    }

    .hero p {
        font-size: 19px;
    }

    .processTimeline {
        margin-top: 50px;
    }

    .processTimeline::before {
        display: none;
    }

    .processStep {
        flex-basis: 100%;
        max-width: 450px;
        margin: 0 auto 40px auto;
    }

    .featuresGrid,
    .successGrid,
    .pricingGrid,
    .blogGrid {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .pricingCard.recommended {
        transform: scale(1);
    }

    .pricingCard.recommended:hover {
        transform: translateY(-10px);
    }

    .shapeDivider svg {
        height: 50px;
    }

    .testimonials, .cta {
        padding-top: 80px;
        padding-bottom: 80px;
    }

    .footerContent {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 30px;
    }

    .socialLinks {
        justify-content: center;
    }

    .footerLinks {
        grid-template-columns: repeat(2, 1fr);
        gap: 30px;
        margin: 30px 0;
    }

    .footerSection h4 {
        font-size: 1rem;
        margin-bottom: 15px;
    }

    .footerSection li {
        margin-bottom: 10px;
    }

    .footerLinks a {
        font-size: 0.9rem;
    }
}

/* Mobile styles for footer */
@media (max-width: 480px) {
    .footerLinks {
        grid-template-columns: 1fr;
        gap: 25px;
        margin: 25px 0;
        text-align: center;
    }

    .footerSection h4::after {
        left: 50%;
        transform: translateX(-50%);
    }

    .pricingTabs {
        flex-direction: column;
        max-width: 300px;
        gap: 10px;
        padding: 15px;
    }

    .pricingTab {
        padding: 12px 20px;
        border-radius: 10px;
    }

    .mediaLogos {
        gap: 20px;
    }

    .mediaLogos img {
        height: 35px;
    }
}

@media (min-width: 993px) {
    .processTimeline::before {
        display: block;
    }
}

@media (max-width: 768px) {
    .hero h1 {
        font-size: 38px;
    }

    .hero p {
        font-size: 17px;
    }

    .heroButtons {
        flex-direction: column;
        align-items: center;
        gap: 15px;
    }

    .heroButtons .btn {
        width: 90%;
        max-width: 300px;
        text-align: center;
    }

    .sectionTitle .subtitle {
        font-size: 17px;
    }

    .testimonialCard {
        padding: 35px 25px;
        min-height: auto;
    }

    .testimonialAuthor {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }

    .authorImage {
        margin-right: 0;
        margin-bottom: 20px;
    }

    .shapeDivider svg {
        height: 40px;
    }

    .testimonials, .cta {
        padding-top: 60px;
        padding-bottom: 60px;
    }
}
