// API route for content pages management
import { API_BASE_URL } from '@/config/apiConfig';

// Mock data for content pages (fallback when backend is not available)
const mockContentPages = [
  {
    id: 1,
    key: 'privacy-policy',
    title: 'Privacy Policy',
    content: 'Privacy policy content...',
    metaTitle: 'Privacy Policy - Vaivahik',
    metaDescription: 'Privacy policy for Vaivahik matrimony platform',
    isPublished: true,
    url: '/legal/privacy-policy',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 2,
    key: 'terms-of-service',
    title: 'Terms of Service',
    content: 'Terms of service content...',
    metaTitle: 'Terms of Service - Vaivahik',
    metaDescription: 'Terms of service for Vaivahik matrimony platform',
    isPublished: true,
    url: '/legal/terms-of-service',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 3,
    key: 'refund-policy',
    title: 'Refund Policy',
    content: 'Refund policy content...',
    metaTitle: 'Refund Policy - Vaivahik',
    metaDescription: 'Refund policy for Vaivahik matrimony platform',
    isPublished: true,
    url: '/legal/refund-policy',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 4,
    key: 'about-vaivahik',
    title: 'About Vaivahik',
    content: 'About Vaivahik content...',
    metaTitle: 'About Vaivahik - AI Matrimony Platform',
    metaDescription: 'Learn about Vaivahik, the world\'s most advanced AI-powered matrimony platform',
    isPublished: true,
    url: '/about',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 5,
    key: 'contact-us',
    title: 'Contact Us',
    content: 'Contact us content...',
    metaTitle: 'Contact Us - Vaivahik',
    metaDescription: 'Get in touch with Vaivahik support team',
    isPublished: true,
    url: '/contact',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
];

export default async function handler(req, res) {
  const { method } = req;

  try {
    switch (method) {
      case 'GET':
        // Try to fetch from backend first
        try {
          const backendResponse = await fetch(`${API_BASE_URL}/admin/content-pages`, {
            headers: {
              'Content-Type': 'application/json',
              'Authorization': req.headers.authorization || ''
            }
          });

          if (backendResponse.ok) {
            const data = await backendResponse.json();
            return res.status(200).json(data.pages || data);
          }
        } catch (backendError) {
          console.log('Backend not available, using mock data');
        }

        // Fallback to mock data
        return res.status(200).json(mockContentPages);

      case 'POST':
        // Try to create in backend first
        try {
          const backendResponse = await fetch(`${API_BASE_URL}/admin/content-pages`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': req.headers.authorization || ''
            },
            body: JSON.stringify(req.body)
          });

          if (backendResponse.ok) {
            const data = await backendResponse.json();
            return res.status(201).json(data);
          }
        } catch (backendError) {
          console.log('Backend not available, using mock response');
        }

        // Mock response for creation
        const newPage = {
          id: Date.now(),
          ...req.body,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        
        return res.status(201).json({
          success: true,
          message: 'Content page created successfully (mock)',
          page: newPage
        });

      default:
        res.setHeader('Allow', ['GET', 'POST']);
        return res.status(405).json({ 
          success: false, 
          message: `Method ${method} not allowed` 
        });
    }
  } catch (error) {
    console.error('Content pages API error:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Internal server error',
      error: error.message 
    });
  }
}
