{"timestamp": "2025-07-08T16:29:56.234Z", "testType": "Quick Functionality Test", "results": {"files": {"src/pages/admin/dashboard.js": {"exists": true, "path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\VaivahikProject\\vaivahik-nextjs\\src\\pages\\admin\\dashboard.js", "size": 103131}, "src/pages/admin/users.js": {"exists": true, "path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\VaivahikProject\\vaivahik-nextjs\\src\\pages\\admin\\users.js", "size": 170571}, "src/pages/admin/content-management.js": {"exists": true, "path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\VaivahikProject\\vaivahik-nextjs\\src\\pages\\admin\\content-management.js", "size": 16426}, "src/pages/admin/verification-queue.js": {"exists": true, "path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\VaivahikProject\\vaivahik-nextjs\\src\\pages\\admin\\verification-queue.js", "size": 68857}, "src/pages/admin/premium-plans.js": {"exists": true, "path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\VaivahikProject\\vaivahik-nextjs\\src\\pages\\admin\\premium-plans.js", "size": 40150}, "src/pages/website/dashboard.js": {"exists": true, "path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\VaivahikProject\\vaivahik-nextjs\\src\\pages\\website\\dashboard.js", "size": 70796}, "src/website/landing-page-new.js": {"exists": true, "path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\VaivahikProject\\vaivahik-nextjs\\src\\website\\landing-page-new.js", "size": 46783}, "src/website/pages/register.js": {"exists": true, "path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\VaivahikProject\\vaivahik-nextjs\\src\\website\\pages\\register.js", "size": 4963}, "src/website/pages/login.js": {"exists": true, "path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\VaivahikProject\\vaivahik-nextjs\\src\\website\\pages\\login.js", "size": 11102}, "src/pages/api/auth/login.js": {"exists": false, "path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\VaivahikProject\\vaivahik-nextjs\\src\\pages\\api\\auth\\login.js", "size": 0}, "src/pages/api/users/register.js": {"exists": true, "path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\VaivahikProject\\vaivahik-nextjs\\src\\pages\\api\\users\\register.js", "size": 6028}, "src/pages/api/content-pages.js": {"exists": true, "path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\VaivahikProject\\vaivahik-nextjs\\src\\pages\\api\\content-pages.js", "size": 4311}, "src/pages/api/social-media-links.js": {"exists": true, "path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\VaivahikProject\\vaivahik-nextjs\\src\\pages\\api\\social-media-links.js", "size": 3725}, "src/components/admin/EnhancedAdminLayout.js": {"exists": true, "path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\VaivahikProject\\vaivahik-nextjs\\src\\components\\admin\\EnhancedAdminLayout.js", "size": 32448}, "src/components/enhanced/EnhancedMatchDashboard.js": {"exists": true, "path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\VaivahikProject\\vaivahik-nextjs\\src\\components\\enhanced\\EnhancedMatchDashboard.js", "size": 15786}, "src/config/apiConfig.js": {"exists": true, "path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\VaivahikProject\\vaivahik-nextjs\\src\\config\\apiConfig.js", "size": 8141}, "src/utils/axiosConfig.js": {"exists": true, "path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\VaivahikProject\\vaivahik-nextjs\\src\\utils\\axiosConfig.js", "size": 4964}, "next.config.js": {"exists": true, "path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\VaivahikProject\\vaivahik-nextjs\\next.config.js", "size": 2111}, "package.json": {"exists": true, "path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\VaivahikProject\\vaivahik-nextjs\\package.json", "size": 2091}}, "pages": {"Next.js Frontend": {"running": true, "port": 3000}, "Express Backend": {"running": false, "port": 8000}}, "apis": {"Content Pages API": {"success": true, "status": 200, "url": "http://localhost:3000/api/content-pages"}, "Social Media API": {"success": true, "status": 200, "url": "http://localhost:3000/api/social-media-links"}, "Health Check": {"success": true, "status": 200, "url": "http://localhost:3000/api/hello"}}, "summary": {"total": 32, "passed": 30, "failed": 2}}, "recommendations": ["Missing 1 critical files", "1 servers are not running"]}