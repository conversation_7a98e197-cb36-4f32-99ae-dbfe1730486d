/**
 * API Configuration
 * 
 * Central configuration for API endpoints and base URLs
 */

// Environment-based API configuration
const isDevelopment = process.env.NODE_ENV === 'development';
const isProduction = process.env.NODE_ENV === 'production';

// API Base URLs
export const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 
  (isDevelopment ? 'http://localhost:8000/api' : 'https://api.vaivahik.com/api');

export const SOCKET_URL = process.env.NEXT_PUBLIC_SOCKET_URL || 
  (isDevelopment ? 'http://localhost:8000' : 'https://api.vaivahik.com');

// API Endpoints
export const API_ENDPOINTS = {
  // Authentication
  AUTH: {
    LOGIN: '/auth/login',
    REGISTER: '/auth/register',
    LOGOUT: '/auth/logout',
    REFRESH: '/auth/refresh',
    VERIFY_OTP: '/auth/verify-otp',
    RESEND_OTP: '/auth/resend-otp',
    FORGOT_PASSWORD: '/auth/forgot-password',
    RESET_PASSWORD: '/auth/reset-password'
  },

  // User Management
  USER: {
    PROFILE: '/user/profile',
    UPDATE_PROFILE: '/user/update-profile',
    UPLOAD_PHOTO: '/user/upload-photo',
    DELETE_PHOTO: '/user/delete-photo',
    PRIVACY_SETTINGS: '/user/privacy-settings',
    ACCOUNT_SETTINGS: '/user/account-settings',
    DEACTIVATE: '/user/deactivate',
    DELETE_ACCOUNT: '/user/delete-account'
  },

  // Matching & Discovery
  MATCHING: {
    GET_MATCHES: '/matching/get-matches',
    SEND_INTEREST: '/matching/send-interest',
    RESPOND_INTEREST: '/matching/respond-interest',
    GET_INTERESTS: '/matching/get-interests',
    SEARCH_PROFILES: '/matching/search-profiles',
    SAVE_SEARCH: '/matching/save-search',
    GET_SAVED_SEARCHES: '/matching/get-saved-searches'
  },

  // Messaging
  MESSAGING: {
    GET_CONVERSATIONS: '/messaging/conversations',
    GET_MESSAGES: '/messaging/messages',
    SEND_MESSAGE: '/messaging/send-message',
    MARK_READ: '/messaging/mark-read',
    DELETE_CONVERSATION: '/messaging/delete-conversation'
  },

  // Payments & Subscriptions
  PAYMENTS: {
    PLANS: '/payments/plans',
    CREATE_ORDER: '/payments/create-order',
    VERIFY_PAYMENT: '/payments/verify-payment',
    SUBSCRIPTION_STATUS: '/payments/subscription-status',
    CANCEL_SUBSCRIPTION: '/payments/cancel-subscription',
    PAYMENT_HISTORY: '/payments/history'
  },

  // Birthday & Special Features
  BIRTHDAY: {
    CHECK_BIRTHDAY: '/birthday/check',
    GET_WISHES: '/birthday/wishes',
    SEND_WISH: '/birthday/send-wish',
    GET_BIRTHDAY_OFFERS: '/birthday/offers'
  },

  // Admin & Content
  ADMIN: {
    CONTENT_PAGES: '/admin/content-pages',
    SOCIAL_MEDIA_LINKS: '/admin/social-media-links',
    ANALYTICS: '/admin/analytics',
    USER_MANAGEMENT: '/admin/users',
    REPORTS: '/admin/reports'
  },

  // Notifications
  NOTIFICATIONS: {
    GET_NOTIFICATIONS: '/notifications',
    MARK_READ: '/notifications/mark-read',
    UPDATE_SETTINGS: '/notifications/settings',
    SUBSCRIBE_PUSH: '/notifications/subscribe-push'
  },

  // AI & ML
  AI: {
    ADVANCED_MATCHES: '/ai/advanced-matches',
    USER_BEHAVIOR: '/ai/user-behavior',
    LEARNED_PREFERENCES: '/ai/learned-preferences',
    PERSONALIZED_CONTENT: '/ai/personalized-content',
    CONVERSATION_STARTERS: '/ai/conversation-starters',
    PROFILE_OPTIMIZATION: '/ai/profile-optimization',
    PREDICTIVE_INSIGHTS: '/ai/predictive-insights',
    LEARN_FROM_ACTION: '/ai/learn-from-action',
    OPTIMAL_NOTIFICATION_TIME: '/ai/optimal-notification-time',
    COMMON_INTERESTS: '/ai/common-interests'
  }
};

// Request configuration
export const API_CONFIG = {
  timeout: 30000, // 30 seconds
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
};

// Helper function to build full API URL
export const buildApiUrl = (endpoint) => {
  return `${API_BASE_URL}${endpoint}`;
};

// Helper function to get authorization headers
export const getAuthHeaders = () => {
  if (typeof window !== 'undefined') {
    const token = localStorage.getItem('authToken');
    if (token) {
      return {
        'Authorization': `Bearer ${token}`
      };
    }
  }
  return {};
};

// Helper function to make authenticated requests
export const makeAuthenticatedRequest = async (endpoint, options = {}) => {
  const authHeaders = getAuthHeaders();
  const url = buildApiUrl(endpoint);
  
  const requestOptions = {
    ...API_CONFIG,
    ...options,
    headers: {
      ...API_CONFIG.headers,
      ...authHeaders,
      ...options.headers
    }
  };

  try {
    const response = await fetch(url, requestOptions);
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('API request failed:', error);
    throw error;
  }
};

export default {
  API_BASE_URL,
  SOCKET_URL,
  API_ENDPOINTS,
  API_CONFIG,
  buildApiUrl,
  getAuthHeaders,
  makeAuthenticatedRequest
};
