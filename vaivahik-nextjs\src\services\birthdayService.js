/**
 * Birthday Service
 * 
 * Frontend service for handling birthday-related API calls and functionality.
 */

import { API_BASE_URL, API_ENDPOINTS, makeAuthenticatedRequest } from '../config/api';

class BirthdayService {
  /**
   * Check if today is user's birthday and get birthday wishes
   * @returns {Promise<Object>} Birthday check response
   */
  async checkBirthday() {
    try {
      const response = await makeAuthenticatedRequest(API_ENDPOINTS.BIRTHDAY.CHECK_BIRTHDAY, {
        method: 'GET'
      });

    } catch (error) {
      console.error('Error checking birthday:', error);
      throw error;
    }
  }

  /**
   * Get birthday wishes for user (can be called anytime for testing)
   * @returns {Promise<Object>} Birthday wishes response
   */
  async getBirthdayWishes() {
    try {
      const response = await makeAuthenticatedRequest(API_ENDPOINTS.BIRTHDAY.GET_WISHES, {
        method: 'GET'
      });

    } catch (error) {
      console.error('Error getting birthday wishes:', error);
      throw error;
    }
  }

  /**
   * Send birthday notification to user
   * @returns {Promise<Object>} Notification response
   */
  async sendBirthdayNotification() {
    try {
      const response = await makeAuthenticatedRequest(API_ENDPOINTS.BIRTHDAY.SEND_WISH, {
        method: 'POST'
      });

    } catch (error) {
      console.error('Error sending birthday notification:', error);
      throw error;
    }
  }

  /**
   * Check if birthday wishes should be shown
   * This checks localStorage to avoid showing wishes multiple times per day
   * @returns {boolean} Whether to show birthday wishes
   */
  shouldShowBirthdayWishes() {
    const today = new Date().toDateString();
    const lastShown = localStorage.getItem('birthdayWishesShown');
    
    return lastShown !== today;
  }

  /**
   * Mark birthday wishes as shown for today
   */
  markBirthdayWishesShown() {
    const today = new Date().toDateString();
    localStorage.setItem('birthdayWishesShown', today);
  }

  /**
   * Get birthday celebration animation preferences
   * @returns {Object} Animation preferences
   */
  getAnimationPreferences() {
    const preferences = localStorage.getItem('birthdayAnimationPreferences');
    return preferences ? JSON.parse(preferences) : {
      enableConfetti: true,
      enableSound: false, // Default to false to avoid auto-play issues
      animationDuration: 3000
    };
  }

  /**
   * Save birthday celebration animation preferences
   * @param {Object} preferences Animation preferences
   */
  saveAnimationPreferences(preferences) {
    localStorage.setItem('birthdayAnimationPreferences', JSON.stringify(preferences));
  }

  /**
   * Format birthday wishes for display
   * @param {Object} birthdayData Raw birthday data from API
   * @returns {Object} Formatted birthday data
   */
  formatBirthdayWishes(birthdayData) {
    if (!birthdayData?.birthdayWishes) {
      return null;
    }

    const wishes = birthdayData.birthdayWishes;
    
    return {
      ...wishes,
      formattedAge: wishes.age ? `${wishes.age} years young` : null,
      hasSpecialOffers: wishes.specialOffers && wishes.specialOffers.length > 0,
      validOffers: wishes.specialOffers?.filter(offer => 
        new Date(offer.validUntil) > new Date()
      ) || []
    };
  }

  /**
   * Get birthday statistics for admin
   * @returns {Promise<Object>} Birthday statistics
   */
  async getBirthdayStats() {
    try {
      const response = await makeAuthenticatedRequest('/admin/birthday/stats', {
        method: 'GET'
      });

      return response;
    } catch (error) {
      console.error('Error getting birthday stats:', error);
      throw error;
    }
  }

  /**
   * Get users with birthdays today (Admin only)
   * @returns {Promise<Object>} Today's birthday users
   */
  async getTodaysBirthdays() {
    try {
      const response = await makeAuthenticatedRequest('/admin/birthday/today', {
        method: 'GET'
      });

      return response;
    } catch (error) {
      console.error('Error getting today\'s birthdays:', error);
      throw error;
    }
  }

  /**
   * Send birthday notifications to all users with birthdays today (Admin only)
   * @returns {Promise<Object>} Notification results
   */
  async notifyAllBirthdayUsers() {
    try {
      const response = await makeAuthenticatedRequest('/admin/birthday/notify-all', {
        method: 'POST'
      });

      return response;
    } catch (error) {
      console.error('Error notifying all birthday users:', error);
      throw error;
    }
  }
}

export default new BirthdayService();
