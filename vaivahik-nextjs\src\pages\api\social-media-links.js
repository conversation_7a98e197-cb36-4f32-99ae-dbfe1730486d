// API route for social media links management
import { API_BASE_URL } from '@/config/apiConfig';

// Mock data for social media links (fallback when backend is not available)
const mockSocialMediaLinks = [
  {
    id: 1,
    platform: 'facebook',
    url: 'https://facebook.com/vaivahik',
    iconClass: 'fab fa-facebook-f',
    isActive: true,
    displayOrder: 1,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 2,
    platform: 'twitter',
    url: 'https://twitter.com/vaivahik',
    iconClass: 'fab fa-twitter',
    isActive: true,
    displayOrder: 2,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 3,
    platform: 'instagram',
    url: 'https://instagram.com/vaivahik',
    iconClass: 'fab fa-instagram',
    isActive: true,
    displayOrder: 3,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 4,
    platform: 'linkedin',
    url: 'https://linkedin.com/company/vaivahik',
    iconClass: 'fab fa-linkedin-in',
    isActive: true,
    displayOrder: 4,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 5,
    platform: 'youtube',
    url: 'https://youtube.com/@vaivahik',
    iconClass: 'fab fa-youtube',
    isActive: true,
    displayOrder: 5,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
];

export default async function handler(req, res) {
  const { method } = req;

  try {
    switch (method) {
      case 'GET':
        // Try to fetch from backend first
        try {
          const backendResponse = await fetch(`${API_BASE_URL}/admin/social-media-links`, {
            headers: {
              'Content-Type': 'application/json',
              'Authorization': req.headers.authorization || ''
            }
          });

          if (backendResponse.ok) {
            const data = await backendResponse.json();
            return res.status(200).json(data.links || data);
          }
        } catch (backendError) {
          console.log('Backend not available, using mock data');
        }

        // Fallback to mock data
        return res.status(200).json(mockSocialMediaLinks);

      case 'POST':
        // Try to create in backend first
        try {
          const backendResponse = await fetch(`${API_BASE_URL}/admin/social-media-links`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': req.headers.authorization || ''
            },
            body: JSON.stringify(req.body)
          });

          if (backendResponse.ok) {
            const data = await backendResponse.json();
            return res.status(201).json(data);
          }
        } catch (backendError) {
          console.log('Backend not available, using mock response');
        }

        // Mock response for creation
        const newLink = {
          id: Date.now(),
          ...req.body,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        
        return res.status(201).json({
          success: true,
          message: 'Social media link created successfully (mock)',
          link: newLink
        });

      default:
        res.setHeader('Allow', ['GET', 'POST']);
        return res.status(405).json({ 
          success: false, 
          message: `Method ${method} not allowed` 
        });
    }
  } catch (error) {
    console.error('Social media links API error:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Internal server error',
      error: error.message 
    });
  }
}
